import React, { useState, useRef, useEffect } from 'react';
import { Fab, Drawer, useMediaQuery, useTheme, Zoom, Tooltip, alpha } from '@mui/material';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import AIAssistantChat from './AIAssistantChat';

const FloatingAssistantButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 20, y: 20 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const fabRef = useRef<HTMLButtonElement>(null);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const toggleDrawer = () => {
    if (!isDragging) {
      setIsOpen(!isOpen);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(false);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (dragStart.x !== 0 || dragStart.y !== 0) {
      setIsDragging(true);
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      // Keep within viewport bounds
      const maxX = window.innerWidth - 56; // FAB width
      const maxY = window.innerHeight - 56; // FAB height

      setPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY))
      });
    }
  };

  const handleMouseUp = () => {
    setDragStart({ x: 0, y: 0 });
    // Reset dragging state after a short delay to prevent click event
    setTimeout(() => setIsDragging(false), 100);
  };

  useEffect(() => {
    if (dragStart.x !== 0 || dragStart.y !== 0) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [dragStart]);

  return (
    <>
      <Zoom in={true} style={{ transitionDelay: '500ms' }}>
        <Tooltip title="AI Assistant (Drag to move)" placement="left" TransitionComponent={Zoom}>
          <Fab
            ref={fabRef}
            size="small"
            color="primary"
            aria-label="AI Assistant"
            sx={{
              position: 'fixed',
              top: `${position.y}px`,
              right: `${position.x}px`,
              zIndex: 1000,
              width: 48,
              height: 48,
              background: theme.palette.mode === 'dark'
                ? `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`
                : `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.5)}`,
              transition: isDragging ? 'none' : 'all 0.3s ease-in-out',
              cursor: isDragging ? 'grabbing' : 'grab',
              '&:hover': {
                transform: isDragging ? 'none' : 'scale(1.05)',
                boxShadow: `0 6px 25px ${alpha(theme.palette.primary.main, 0.6)}`,
              },
            }}
            onClick={toggleDrawer}
            onMouseDown={handleMouseDown}
          >
            <AutoAwesomeIcon
              fontSize="small"
              sx={{
                animation: isDragging ? 'none' : 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': { opacity: 0.8, transform: 'scale(1)' },
                  '50%': { opacity: 1, transform: 'scale(1.1)' },
                  '100%': { opacity: 0.8, transform: 'scale(1)' },
                }
              }}
            />
          </Fab>
        </Tooltip>
      </Zoom>

      <Drawer
        anchor="right"
        open={isOpen}
        onClose={toggleDrawer}
        transitionDuration={{ enter: 500, exit: 300 }}
        PaperProps={{
          sx: {
            width: isMobile ? '100%' : '400px',
            borderTopLeftRadius: isMobile ? 0 : 16,
            borderBottomLeftRadius: isMobile ? 0 : 16,
            overflow: 'hidden',
            boxShadow: theme.shadows[10],
            background: theme.palette.mode === 'dark'
              ? theme.palette.background.paper
              : 'white',
          },
        }}
        SlideProps={{
          appear: true,
          direction: "left"
        }}
      >
        <AIAssistantChat onClose={toggleDrawer} />
      </Drawer>
    </>
  );
};

export default FloatingAssistantButton;
